<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-white justify-between group/design-root overflow-x-hidden"
      style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'
    >
      <div>
        <div class="flex items-center bg-white p-4 pb-2 justify-between">
          <h2 class="text-[#181111] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pl-12">补货</h2>
          <div class="flex w-12 items-center justify-end">
            <button
              class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-12 bg-transparent text-[#181111] gap-2 text-base font-bold leading-normal tracking-[0.015em] min-w-0 p-0"
            >
              <div class="text-[#181111]" data-icon="ShoppingCart" data-size="24px" data-weight="regular">
                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                  <path
                    d="M222.14,58.87A8,8,0,0,0,216,56H54.68L49.79,29.14A16,16,0,0,0,34.05,16H16a8,8,0,0,0,0,16h18L59.56,172.29a24,24,0,0,0,5.33,11.27,28,28,0,1,0,44.4,8.44h45.42A27.75,27.75,0,0,0,152,204a28,28,0,1,0,28-28H83.17a8,8,0,0,1-7.87-6.57L72.13,152h116a24,24,0,0,0,23.61-19.71l12.16-66.86A8,8,0,0,0,222.14,58.87ZM96,204a12,12,0,1,1-12-12A12,12,0,0,1,96,204Zm96,0a12,12,0,1,1-12-12A12,12,0,0,1,192,204Zm4-74.57A8,8,0,0,1,188.1,136H69.22L57.59,72H206.41Z"
                  ></path>
                </svg>
              </div>
            </button>
          </div>
        </div>
        <div class="flex gap-3 p-3 overflow-x-hidden">
          <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#f4f0f0] pl-4 pr-2">
            <p class="text-[#181111] text-sm font-medium leading-normal">分类</p>
            <div class="text-[#181111]" data-icon="CaretDown" data-size="20px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
              </svg>
            </div>
          </button>
          <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#f4f0f0] pl-4 pr-2">
            <p class="text-[#181111] text-sm font-medium leading-normal">价格</p>
            <div class="text-[#181111]" data-icon="CaretDown" data-size="20px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
              </svg>
            </div>
          </button>
          <button class="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#f4f0f0] pl-4 pr-2">
            <p class="text-[#181111] text-sm font-medium leading-normal">评分</p>
            <div class="text-[#181111]" data-icon="CaretDown" data-size="20px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
              </svg>
            </div>
          </button>
        </div>
        <div class="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
          <div class="flex flex-col gap-3 pb-3">
            <div
              class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuAip1I6yXKwoZwMR4ljn9T4CHSGH7KX3dcSGubjck_3ep0xaXLylgqTumYL-IriZ8lVPsIhUg3knFHw3Hbit0NlMtRUa-FtJ6DZfuVVQ7zu-dg3ugWPnneY0qZChrpRkS7uOxp9PZsJlTG8bsapLw0SNsFLn-hHAQeaViU9sqHJ6P5eRCUtRi8AtYTKWpZirIgTKi6iyRgV6R5u18-qnytgjbWLLc27cqpur6iAxIuOju9O53N2hx2Ed7vQ0RjnvJ3UUtno5c90Z94");'
            ></div>
            <div>
              <p class="text-[#181111] text-base font-medium leading-normal">项链</p>
              <p class="text-[#886364] text-sm font-normal leading-normal">属性：金属，数量：5，订单号：12345</p>
            </div>
          </div>
          <div class="flex flex-col gap-3 pb-3">
            <div
              class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuD81CH6zHavlhRA_3lfp_tJPfl9IL756ObouhjMuXgTk0jTkEDu2hOsq7oaxlSJIhpbUWLuPmvhO_TJWFAnYICLrYgqUH8NfErwo0Z0XKJtez2Ku83pHqz24Qr7v95aRKPgv2j702woxmmrpPbzQG6SxuW4KNQa62OYBiGZaFhHR-unDi7A6JdJ8eimNhvQXOTdNSLUPo-Qoto4XJw0i-Q7c4ZVbyBW_iBFjlLtF4FT1O4B8DZgHNFNc4cOzCGk-BJ4hKmGUOyHJUQ");'
            ></div>
            <div>
              <p class="text-[#181111] text-base font-medium leading-normal">手链</p>
              <p class="text-[#886364] text-sm font-normal leading-normal">属性：珍珠，数量：3，订单号：67890</p>
            </div>
          </div>
          <div class="flex flex-col gap-3 pb-3">
            <div
              class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDp8eDfUZuKrrB9a1vxS9rpZWxGKnUyekmwyWfr0pSacbdCvFjrpJ-9j7pOMVukgJv23enz6E2Jv208T36b0CrGpDKQXl6_bcZLbgFtnQfd1fsoVAvfTE-8bpmfNnRBYqdLl06AofqpNkcIL3g0R0dmCvjYCqyP002mijpkSjUjb5z92V_neju_gLCb-6CM9hXNEUTJ3u4IJNENwWIDqqXZeTGHqj60o__BGrrYZY_tgN4xTbdjAxz_dqdTX1d9qq0LKJBVpI4RxE0");'
            ></div>
            <div>
              <p class="text-[#181111] text-base font-medium leading-normal">耳环</p>
              <p class="text-[#886364] text-sm font-normal leading-normal">属性：钻石，数量：10，订单号：11223</p>
            </div>
          </div>
          <div class="flex flex-col gap-3 pb-3">
            <div
              class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCJPH0ageqDBt1H8pt5KphRrW7mHAoh0sTs_6nsVoIPydvJTkjrMJvwTzuqkDLF5l4aJaXgyk4AVYoREcX1VEjCuHFyz8HC2rzXpKNPsXqfjg7XNOY1IRMZQcC_P-zXO2vYS4o5vpbqkS01IbnO2P4V1VpMkXNMx4m1-7d4OEDnoi3d8ZVkLXbVx8dJEF8e6f3GzY8-MfUIVkIVyYivjqQlMT1DTqZW0CSF9cunENIZB1e6VZcfCESfu23b3fcXakaydoPrq009PAs");'
            ></div>
            <div>
              <p class="text-[#181111] text-base font-medium leading-normal">戒指</p>
              <p class="text-[#886364] text-sm font-normal leading-normal">属性：银，数量：8，订单号：44556</p>
            </div>
          </div>
          <div class="flex flex-col gap-3 pb-3">
            <div
              class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBdwescWwZiYbQhrAEUkZWzLyoLwzImVrSygFm8KuY58oFtkrP9vjM8cm3Q3Ez9_SHdEtdsnn4DZkwYfsb1mtOk2gKmQrNbv2iqDXNikb2QtCYRacuJ-qm5xTby8FBNFZnKxhEpH6Q0Vn_hUlS2qg3AoFIEKsnMMjbWPNID9OQQCbYqcqHpCioeck0DmDKa-Xm5Qy7eKVaJ3xob-eBFC34R258S5AME0a-mzyfzFT-P9uAC_TDGCC2be-y3RE9QkbtLRLAHX054SRg");'
            ></div>
            <div>
              <p class="text-[#181111] text-base font-medium leading-normal">胸针</p>
              <p class="text-[#886364] text-sm font-normal leading-normal">属性：彩色宝石，数量：6，订单号：77889</p>
            </div>
          </div>
          <div class="flex flex-col gap-3 pb-3">
            <div
              class="w-full bg-center bg-no-repeat aspect-square bg-cover rounded-xl"
              style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDHkZ3VXBkfUtItBgocT4ol1Mg6PDTISBr9ArL9g0K6iH5SlN23CX0RtDF01CHdU-Y0tlkEifl8MdJu9iddDVQZUOU3MBQZ9qBR0MIpSF-vi7VsoM44dOL6AnQ16HzkaTvkI86UgzzBaD7WAuBNIXGq-p84fKpYswg8Nc2PacgcjsyVg8pOKnWRTdj4gOiLS_IKxD7VbWToWwpBbAKs8_8WLJo3rjdnKvgjNMtsoQ5C0mTSHR6mWz_b-FPqrrCJslYHTBoP8twWUgQ");'
            ></div>
            <div>
              <p class="text-[#181111] text-base font-medium leading-normal">发饰</p>
              <p class="text-[#886364] text-sm font-normal leading-normal">属性：复古，数量：4，订单号：99001</p>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="flex gap-2 border-t border-[#f4f0f0] bg-white px-4 pb-3 pt-2">
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#886364]" href="#">
            <div class="text-[#886364] flex h-8 items-center justify-center" data-icon="House" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#886364] text-xs font-medium leading-normal tracking-[0.015em]">首页</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 rounded-full text-[#181111]" href="#">
            <div class="text-[#181111] flex h-8 items-center justify-center" data-icon="Package" data-size="24px" data-weight="fill">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M223.68,66.15,135.68,18a15.88,15.88,0,0,0-15.36,0l-88,48.17a16,16,0,0,0-8.32,14v95.64a16,16,0,0,0,8.32,14l88,48.17a15.88,15.88,0,0,0,15.36,0l88-48.17a16,16,0,0,0,8.32-14V80.18A16,16,0,0,0,223.68,66.15ZM128,32l80.35,44L178.57,92.29l-80.35-44Zm0,88L47.65,76,81.56,57.43l80.35,44Zm88,55.85h0l-80,43.79V133.83l32-17.51V152a8,8,0,0,0,16,0V107.56l32-17.51v85.76Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#181111] text-xs font-medium leading-normal tracking-[0.015em]">待拿</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#886364]" href="#">
            <div class="text-[#886364] flex h-8 items-center justify-center" data-icon="Receipt" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M72,104a8,8,0,0,1,8-8h96a8,8,0,0,1,0,16H80A8,8,0,0,1,72,104Zm8,40h96a8,8,0,0,0,0-16H80a8,8,0,0,0,0,16ZM232,56V208a8,8,0,0,1-11.58,7.15L192,200.94l-28.42,14.21a8,8,0,0,1-7.16,0L128,200.94,99.58,215.15a8,8,0,0,1-7.16,0L64,200.94,35.58,215.15A8,8,0,0,1,24,208V56A16,16,0,0,1,40,40H216A16,16,0,0,1,232,56Zm-16,0H40V195.06l20.42-10.22a8,8,0,0,1,7.16,0L96,199.06l28.42-14.22a8,8,0,0,1,7.16,0L160,199.06l28.42-14.22a8,8,0,0,1,7.16,0L216,195.06Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#886364] text-xs font-medium leading-normal tracking-[0.015em]">订货</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#886364]" href="#">
            <div class="text-[#886364] flex h-8 items-center justify-center" data-icon="Truck" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M247.42,117l-14-35A15.93,15.93,0,0,0,218.58,72H184V64a8,8,0,0,0-8-8H24A16,16,0,0,0,8,72V184a16,16,0,0,0,16,16H41a32,32,0,0,0,62,0h50a32,32,0,0,0,62,0h17a16,16,0,0,0,16-16V120A7.94,7.94,0,0,0,247.42,117ZM184,88h34.58l9.6,24H184ZM24,72H168v64H24ZM72,208a16,16,0,1,1,16-16A16,16,0,0,1,72,208Zm81-24H103a32,32,0,0,0-62,0H24V152H168v12.31A32.11,32.11,0,0,0,153,184Zm31,24a16,16,0,1,1,16-16A16,16,0,0,1,184,208Zm48-24H215a32.06,32.06,0,0,0-31-24V128h48Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#886364] text-xs font-medium leading-normal tracking-[0.015em]">待发</p>
          </a>
          <a class="just flex flex-1 flex-col items-center justify-end gap-1 text-[#886364]" href="#">
            <div class="text-[#886364] flex h-8 items-center justify-center" data-icon="User" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#886364] text-xs font-medium leading-normal tracking-[0.015em]">我的</p>
          </a>
        </div>
        <div class="h-5 bg-white"></div>
      </div>
    </div>
  </body>
</html>
